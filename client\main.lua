local isShopOpen = false
local inMarker = false
local ESX = nil

-- Initialize ESX
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(1000)
    end
end)

-- Function to open shop UI
function OpenShopUI()
    isShopOpen = true
    SetNuiFocus(true, true)

    -- Send shop data to UI
    SendNUIMessage({
        type = "openShop",
        items = Config.Items,
        imagePath = Config.ImagePath
    })
end

-- Function to close shop UI
function CloseShopUI()
    isShopOpen = false
    SetNuiFocus(false, false)

    SendNUIMessage({
        type = "closeShop"
    })
end

-- Create single shop blip
Citizen.CreateThread(function()
    local blip = AddBlipForCoord(Config.ShopBlip.coords)
    SetBlipSprite(blip, Config.ShopBlip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.ShopBlip.scale)
    SetBlipColour(blip, Config.ShopBlip.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.ShopBlip.name)
    EndTextCommandSetBlipName(blip)
end)

-- Draw markers and check distance for all markers
Citizen.CreateThread(function()
    while true do
        local sleep = 1000
        local playerCoords = GetEntityCoords(PlayerPedId())
        local nearestMarker = nil
        local nearestDistance = math.huge
        local anyMarkerNearby = false

        -- Check all markers
        for _, marker in pairs(Config.ShopMarkers) do
            local distance = #(playerCoords - marker.coords)
            local drawDistance = marker.drawDistance or Config.DrawDistance

            if distance < drawDistance then
                anyMarkerNearby = true
                sleep = 0

                -- Get marker settings (use marker-specific or global)
                local markerType = marker.markerType or Config.MarkerType
                local markerSize = marker.markerSize or Config.MarkerSize
                local markerColor = marker.markerColor or Config.MarkerColor

                DrawMarker(
                    markerType,
                    marker.coords.x, marker.coords.y, marker.coords.z - 1.0,
                    0.0, 0.0, 0.0,
                    0.0, 0.0, 0.0,
                    markerSize.x, markerSize.y, markerSize.z,
                    markerColor.r, markerColor.g, markerColor.b, markerColor.a,
                    false, true, 2, false, nil, nil, false
                )

                -- Check if this is the nearest marker
                if distance < nearestDistance and distance < 1.5 then
                    nearestDistance = distance
                    nearestMarker = marker
                end
            end
        end

        -- Handle interaction with nearest marker
        if nearestMarker then
            if not inMarker then
                inMarker = true
            end

            -- Display help text
            BeginTextCommandDisplayHelp("STRING")
            AddTextComponentSubstringPlayerName("Press ~INPUT_CONTEXT~ to access the shop")
            EndTextCommandDisplayHelp(0, false, true, -1)

            -- Open shop on E press
            if IsControlJustReleased(0, 38) and not isShopOpen then
                OpenShopUI()
            end
        else
            if inMarker then
                inMarker = false
            end
        end

        Citizen.Wait(sleep)
    end
end)

-- NUI Callbacks
RegisterNUICallback('closeShop', function(data, cb)
    CloseShopUI()
    cb('ok')
end)

RegisterNUICallback('buyItem', function(data, cb)
    TriggerServerEvent('shop:buyItem', data.item, data.quantity)
    cb('ok')
end)

-- Event to notify player
RegisterNetEvent('shop:notify')
AddEventHandler('shop:notify', function(message, notifyType)
    -- Using ox_lib notifications
    if notifyType == 'error' then
        lib.notify({
            title = 'Shop',
            description = message,
            type = 'error',
            position = 'top',
            duration = 3000,
            icon = 'ban',
            iconColor = '#C53030'
        })
    elseif notifyType == 'success' then
        lib.notify({
            title = 'Shop',
            description = message,
            type = 'success',
            position = 'top',
            duration = 3000,
            icon = 'check-circle',
            iconColor = '#2ECC71'
        })
    else
        lib.notify({
            title = 'Shop',
            description = message,
            type = 'inform',
            position = 'top',
            duration = 3000,
            icon = 'info-circle',
            iconColor = '#3498DB'
        })
    end
end)

-- Close shop if player moves too far away from any marker
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        if isShopOpen then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local nearAnyMarker = false

            -- Check if player is near any marker
            for _, marker in pairs(Config.ShopMarkers) do
                local distance = #(playerCoords - marker.coords)
                if distance <= 3.0 then
                    nearAnyMarker = true
                    break
                end
            end

            -- Close shop if not near any marker
            if not nearAnyMarker then
                CloseShopUI()
            end
        end
    end
end)
