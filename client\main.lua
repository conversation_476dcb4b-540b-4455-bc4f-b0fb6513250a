local isShopOpen = false
local inMarker = false
local currentShop = nil
local ESX = nil

-- Initialize ESX
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(1000)
    end
end)

-- Function to open shop UI
function OpenShopUI(shop)
    isShopOpen = true
    currentShop = shop
    SetNuiFocus(true, true)

    -- Get items for this shop
    local shopItems = Config.Items
    if shop.items ~= "default" and type(shop.items) == "table" then
        shopItems = shop.items
    end

    -- Send shop data to UI
    SendNUIMessage({
        type = "openShop",
        items = shopItems,
        imagePath = Config.ImagePath,
        shopName = shop.name
    })
end

-- Function to close shop UI
function CloseShopUI()
    isShopOpen = false
    currentShop = nil
    SetNuiFocus(false, false)

    SendNUIMessage({
        type = "closeShop"
    })
end

-- Create shop blips
Citizen.CreateThread(function()
    for _, shop in pairs(Config.Shops) do
        local blip = AddBlipForCoord(shop.blipCoords)
        SetBlipSprite(blip, shop.blipSprite or 617)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, shop.blipScale or 0.7)
        SetBlipColour(blip, shop.blipColor or 3)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(shop.name)
        EndTextCommandSetBlipName(blip)
    end
end)

-- Draw markers and check distance for all shops
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        local playerCoords = GetEntityCoords(PlayerPedId())
        local nearestShop = nil
        local nearestDistance = math.huge

        -- Check all shops
        for _, shop in pairs(Config.Shops) do
            local distance = #(playerCoords - shop.markerCoords)
            local drawDistance = shop.drawDistance or Config.DrawDistance

            if distance < drawDistance then
                -- Get marker settings (use shop-specific or global)
                local markerType = shop.markerType or Config.MarkerType
                local markerSize = shop.markerSize or Config.MarkerSize
                local markerColor = shop.markerColor or Config.MarkerColor

                DrawMarker(
                    markerType,
                    shop.markerCoords.x, shop.markerCoords.y, shop.markerCoords.z - 1.0,
                    0.0, 0.0, 0.0,
                    0.0, 0.0, 0.0,
                    markerSize.x, markerSize.y, markerSize.z,
                    markerColor.r, markerColor.g, markerColor.b, markerColor.a,
                    false, true, 2, false, nil, nil, false
                )

                -- Check if this is the nearest shop
                if distance < nearestDistance and distance < 1.5 then
                    nearestDistance = distance
                    nearestShop = shop
                end
            end
        end

        -- Handle interaction with nearest shop
        if nearestShop then
            if not inMarker then
                inMarker = true
                currentShop = nearestShop
            end

            -- Display help text
            BeginTextCommandDisplayHelp("STRING")
            AddTextComponentSubstringPlayerName("Press ~INPUT_CONTEXT~ to access " .. nearestShop.name)
            EndTextCommandDisplayHelp(0, false, true, -1)

            -- Open shop on E press
            if IsControlJustReleased(0, 38) and not isShopOpen then
                OpenShopUI(nearestShop)
            end
        else
            if inMarker then
                inMarker = false
                currentShop = nil
            end
            Citizen.Wait(500)
        end
    end
end)

-- NUI Callbacks
RegisterNUICallback('closeShop', function(data, cb)
    CloseShopUI()
    cb('ok')
end)

RegisterNUICallback('buyItem', function(data, cb)
    local shopId = currentShop and currentShop.id or nil
    TriggerServerEvent('shop:buyItem', data.item, data.quantity, shopId)
    cb('ok')
end)

-- Event to notify player
RegisterNetEvent('shop:notify')
AddEventHandler('shop:notify', function(message, notifyType)
    -- Using ox_lib notifications
    if notifyType == 'error' then
        lib.notify({
            title = 'Shop',
            description = message,
            type = 'error',
            position = 'top',
            duration = 3000,
            icon = 'ban',
            iconColor = '#C53030'
        })
    elseif notifyType == 'success' then
        lib.notify({
            title = 'Shop',
            description = message,
            type = 'success',
            position = 'top',
            duration = 3000,
            icon = 'check-circle',
            iconColor = '#2ECC71'
        })
    else
        lib.notify({
            title = 'Shop',
            description = message,
            type = 'inform',
            position = 'top',
            duration = 3000,
            icon = 'info-circle',
            iconColor = '#3498DB'
        })
    end
end)

-- Close shop if player moves too far away
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        if isShopOpen and currentShop then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - currentShop.markerCoords)

            if distance > 3.0 then
                CloseShopUI()
            end
        end
    end
end)
