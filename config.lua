Config = {}

-- Image path for item icons
Config.ImagePath = "nui://ox_inventory/web/images"

-- Global marker settings (can be overridden per shop)
Config.MarkerType = 1
Config.MarkerSize = vector3(1.0, 1.0, 1.0)
Config.MarkerColor = {r = 0, g = 128, b = 255, a = 100}
Config.DrawDistance = 10.0

-- Multiple shop locations
Config.Shops = {
    {
        id = "jewelry_shop_1",
        name = "Downtown Jewelry",
        markerCoords = vector3(-1376.3018, -300.6817, 43.7964),
        blipCoords = vector3(-1376.3018, -300.6817, 43.7964),
        blipSprite = 617,
        blipColor = 3,
        blipScale = 0.7,
        -- Optional: Override global marker settings for this shop
        -- markerType = 1,
        -- markerSize = vector3(1.0, 1.0, 1.0),
        -- markerColor = {r = 0, g = 128, b = 255, a = 100},
        -- drawDistance = 10.0,
        items = "default" -- Use default items, or specify custom items table
    },
    {
        id = "jewelry_shop_2",
        name = "Uptown Jewelry",
        markerCoords = vector3(-622.0, -230.5, 38.1),
        blipCoords = vector3(-620.0, -228.0, 38.1), -- Different blip position
        blipSprite = 617,
        blipColor = 5, -- Different color
        blipScale = 0.7,
        items = "default"
    },
    {
        id = "jewelry_shop_3",
        name = "Luxury Chains",
        markerCoords = vector3(127.8, -224.4, 54.6),
        blipCoords = vector3(130.0, -220.0, 54.6), -- Different blip position
        blipSprite = 617,
        blipColor = 46, -- Gold color
        blipScale = 0.8, -- Slightly larger
        items = "default"
    }
}

-- Shop items
Config.Items = {
    {
        label = "Chain 12",
        item = "chain_vino",
        price = 5000000
    },
    {
        label = "Chain 10",
        item = "chain_lean",
        price = 500000
    },
    {
        label = "Chain 3",
        item = "chain_3",
        price = 1000000
    },
    {
        label = "Chain 4",
        item = "chain_4",
        price = 1000000
    },
    {
        label = "Chain 5",
        item = "chain_5",
        price = 1000000
    },
    {
        label = "Chain 6",
        item = "chain_6",
        price = 1000000
    },
    {
        label = "Chain 7",
        item = "chain_7",
        price = 1000000
    },
    {
        label = "Chain 8",
        item = "chain_8",
        price = 1000000
    },
    {
        label = "Chain 9",
        item = "chain_9",
        price = 1000000
    },
    {
        label = "Chain 10",
        item = "chain_10",
        price = 1000000
    },
    {
        label = "Chain 11",
        item = "chain_11",
        price = 1000000
    },
    {
        label = "Chain 12",
        item = "chain_12",
        price = 1000000
    },
    {
        label = "Chain 13",
        item = "chain_13",
        price = 1000000
    },
    {
        label = "Chain 14",
        item = "chain_14",
        price = 1000000
    },
    {
        label = "Chain 15",
        item = "chain_15",
        price = 1000000
    },
    {
        label = "Chain 16",
        item = "chain_16",
        price = 1000000
    },
    {
        label = "Chain 17",
        item = "chain_17",
        price = 1000000
    },
    {
        label = "Chain 18",
        item = "chain_18",
        price = 1000000
    }
}
