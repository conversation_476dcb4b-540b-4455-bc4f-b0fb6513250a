Config = {}

-- Image path for item icons
Config.ImagePath = "nui://ox_inventory/web/images"

-- Global marker settings (can be overridden per shop)
Config.MarkerType = 1
Config.MarkerSize = vector3(1.0, 1.0, 1.0)
Config.MarkerColor = {r = 0, g = 128, b = 255, a = 100}
Config.DrawDistance = 10.0

-- Shop blip location (only one blip)
Config.ShopBlip = {
    coords = vector3(-1376.3018, -300.6817, 43.7964),
    sprite = 617,
    color = 3,
    scale = 0.7,
    name = "Jewelry Shop"
}

-- Multiple marker locations for the same shop
Config.ShopMarkers = {
    {
        coords = vector3(-1376.3018, -300.6817, 43.7964),
        -- Optional: Override global marker settings for this marker
        -- markerType = 1,
        -- markerSize = vector3(1.0, 1.0, 1.0),
        -- markerColor = {r = 0, g = 128, b = 255, a = 100},
        -- drawDistance = 10.0,
    },
    {
        coords = vector3(-1378.5, -302.1, 43.7964), -- Second marker nearby
    },
    {
        coords = vector3(-1374.2, -298.3, 43.7964), -- Third marker nearby
    }
}

-- Shop items
Config.Items = {
    {
        label = "Chain 12",
        item = "chain_vino",
        price = 5000000
    },
    {
        label = "Chain 10",
        item = "chain_lean",
        price = 500000
    },
    {
        label = "Chain 3",
        item = "chain_3",
        price = 1000000
    },
    {
        label = "Chain 4",
        item = "chain_4",
        price = 1000000
    },
    {
        label = "Chain 5",
        item = "chain_5",
        price = 1000000
    },
    {
        label = "Chain 6",
        item = "chain_6",
        price = 1000000
    },
    {
        label = "Chain 7",
        item = "chain_7",
        price = 1000000
    },
    {
        label = "Chain 8",
        item = "chain_8",
        price = 1000000
    },
    {
        label = "Chain 9",
        item = "chain_9",
        price = 1000000
    },
    {
        label = "Chain 10",
        item = "chain_10",
        price = 1000000
    },
    {
        label = "Chain 11",
        item = "chain_11",
        price = 1000000
    },
    {
        label = "Chain 12",
        item = "chain_12",
        price = 1000000
    },
    {
        label = "Chain 13",
        item = "chain_13",
        price = 1000000
    },
    {
        label = "Chain 14",
        item = "chain_14",
        price = 1000000
    },
    {
        label = "Chain 15",
        item = "chain_15",
        price = 1000000
    },
    {
        label = "Chain 16",
        item = "chain_16",
        price = 1000000
    },
    {
        label = "Chain 17",
        item = "chain_17",
        price = 1000000
    },
    {
        label = "Chain 18",
        item = "chain_18",
        price = 1000000
    }
}
