-- Framework variables
local ESX = nil
local ox_inventory = nil

-- Initialize ESX
local function InitializeESX()
    if GetResourceState('es_extended') == 'started' then
        ESX = exports['es_extended']:getSharedObject()
    end
end

-- Check if ox_inventory exists
local function InitializeInventory()
    if GetResourceState('ox_inventory') == 'started' then
        ox_inventory = exports.ox_inventory
    end
end

-- Initialize when resource starts
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        InitializeESX()
        InitializeInventory()
    end
end)

-- Try to initialize ESX on resource load
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(1000)
    end
end)

-- Buy item event
RegisterServerEvent('shop:buyItem')
AddEventHandler('shop:buyItem', function(itemName, quantity)
    local source = source

    -- Validate input
    if not itemName or not quantity or quantity <= 0 then
        TriggerClientEvent('shop:notify', source, 'Invalid purchase request.', 'error')
        return
    end

    -- Check if ESX is available
    if not ESX then
        TriggerClientEvent('shop:notify', source, 'Shop system error: Framework not available.', 'error')
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        TriggerClientEvent('shop:notify', source, 'Shop system error: Player data not found.', 'error')
        return
    end

    -- Find the item in the config
    local itemData = nil
    for _, item in ipairs(Config.Items) do
        if item.item == itemName then
            itemData = item
            break
        end
    end

    -- Check if item exists
    if not itemData then
        TriggerClientEvent('shop:notify', source, 'Item not found in shop.', 'error')
        return
    end

    -- Calculate total price
    local totalPrice = itemData.price * quantity

    -- Check if player has enough money
    local playerMoney = xPlayer.getMoney()

    if playerMoney >= totalPrice then
        -- Check if ox_inventory is available
        if not ox_inventory then
            -- Fallback method if ox_inventory is not available
            xPlayer.removeMoney(totalPrice)
            xPlayer.addInventoryItem(itemName, quantity)
            TriggerClientEvent('shop:notify', source, 'You purchased ' .. quantity .. 'x ' .. itemData.label .. ' for $' .. totalPrice, 'success')
            return
        end

        -- Check if player can carry the item
        local canCarry = ox_inventory:CanCarryItem(source, itemName, quantity)
        if canCarry then
            -- Remove money
            xPlayer.removeMoney(totalPrice)

            -- Add item to inventory
            ox_inventory:AddItem(source, itemName, quantity)

            -- Notify player
            TriggerClientEvent('shop:notify', source, 'You purchased ' .. quantity .. 'x ' .. itemData.label .. ' for $' .. totalPrice, 'success')
        else
            TriggerClientEvent('shop:notify', source, 'You cannot carry this item.', 'error')
        end
    else
        TriggerClientEvent('shop:notify', source, 'You do not have enough money.', 'error')
    end
end)

-- Alternative for non-ESX frameworks (like QBCore)
-- Uncomment and modify as needed
--[[
-- QBCore version of the buy item event
RegisterServerEvent('shop:buyItem')
AddEventHandler('shop:buyItem', function(itemName, quantity)
    local source = source

    -- Validate input
    if not itemName or not quantity or quantity <= 0 then
        TriggerClientEvent('shop:notify', source, 'Invalid purchase request.', 'error')
        return
    end

    -- Check if QBCore is available
    if not QBCore then
        TriggerClientEvent('shop:notify', source, 'Shop system error: Framework not available.', 'error')
        return
    end

    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then
        TriggerClientEvent('shop:notify', source, 'Shop system error: Player data not found.', 'error')
        return
    end

    -- Find the item in the config
    local itemData = nil
    for _, item in ipairs(Config.Items) do
        if item.item == itemName then
            itemData = item
            break
        end
    end

    -- Check if item exists
    if not itemData then
        TriggerClientEvent('shop:notify', source, 'Item not found in shop.', 'error')
        return
    end

    -- Calculate total price
    local totalPrice = itemData.price * quantity

    -- Check if player has enough money
    local playerMoney = Player.Functions.GetMoney('cash')

    if playerMoney >= totalPrice then
        -- Check if ox_inventory is available
        if not ox_inventory then
            -- Fallback method if ox_inventory is not available
            Player.Functions.RemoveMoney('cash', totalPrice)
            Player.Functions.AddItem(itemName, quantity)
            TriggerClientEvent('shop:notify', source, 'You purchased ' .. quantity .. 'x ' .. itemData.label .. ' for $' .. totalPrice, 'success')
            return
        end

        -- Check if player can carry the item
        local canCarry = ox_inventory:CanCarryItem(source, itemName, quantity)
        if canCarry then
            -- Remove money
            Player.Functions.RemoveMoney('cash', totalPrice)

            -- Add item to inventory
            ox_inventory:AddItem(source, itemName, quantity)

            -- Notify player
            TriggerClientEvent('shop:notify', source, 'You purchased ' .. quantity .. 'x ' .. itemData.label .. ' for $' .. totalPrice, 'success')
        else
            TriggerClientEvent('shop:notify', source, 'You cannot carry this item.', 'error')
        end
    else
        TriggerClientEvent('shop:notify', source, 'You do not have enough money.', 'error')
    end
end)
--]]
